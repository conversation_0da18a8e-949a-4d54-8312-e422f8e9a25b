import React from 'react';
import { UniversalChat } from '../index';

/**
 * Example implementations of the UniversalChat component
 * for different user roles and use cases.
 */

// Admin Dashboard Chat - Full featured
export const AdminDashboardChat: React.FC = () => {
  return (
    <div className="h-screen">
      <UniversalChat
        userRole="admin"
        userId="admin-123"
        variant="full"
        theme="admin"
        onChatSelect={(chatId) => {
          console.log('Admin selected chat:', chatId);
        }}
        onMessageSent={(message) => {
          console.log('Admin sent message:', message);
        }}
        onError={(error) => {
          console.error('Admin chat error:', error);
        }}
      />
    </div>
  );
};

// Provider Dashboard Chat - Provider specific features
export const ProviderDashboardChat: React.FC<{ customerId?: string }> = ({ customerId }) => {
  return (
    <div className="h-screen">
      <UniversalChat
        userRole="provider"
        userId="provider-456"
        recipientId={customerId}
        variant="full"
        theme="provider"
        features={{
          // Override default provider features if needed
          hasVideoCall: true, // Enable video calls for this provider
        }}
        onChatSelect={(chatId) => {
          // console.log('Provider selected chat:', chatId);
        }}
        onMessageSent={(message) => {
          // console.log('Provider sent message:', message);
        }}
      />
    </div>
  );
};

// Customer Dashboard Chat - Customer focused
export const CustomerDashboardChat: React.FC<{ providerId?: string }> = ({ providerId }) => {
  return (
    <div className="h-screen">
      <UniversalChat
        userRole="customer"
        userId="customer-789"
        recipientId={providerId}
        variant="full"
        theme="customer"
        onChatSelect={(chatId) => {
          console.log('Customer selected chat:', chatId);
        }}
        onMessageSent={(message) => {
          console.log('Customer sent message:', message);
        }}
      />
    </div>
  );
};

// Compact Chat Widget - For embedding in other pages
export const CompactChatWidget: React.FC<{
  userRole: 'admin' | 'provider' | 'customer';
  userId: string;
  recipientId?: string;
}> = ({ userRole, userId, recipientId }) => {
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <UniversalChat
        userRole={userRole}
        userId={userId}
        recipientId={recipientId}
        variant="compact"
        className="shadow-2xl"
        onMessageSent={(message) => {
          console.log('Compact widget message sent:', message);
        }}
      />
    </div>
  );
};

// Modal Chat - For popup conversations
export const ModalChat: React.FC<{
  userRole: 'admin' | 'provider' | 'customer';
  userId: string;
  chatId?: string;
  isOpen: boolean;
  onClose: () => void;
}> = ({ userRole, userId, chatId, isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <UniversalChat
        userRole={userRole}
        userId={userId}
        chatId={chatId}
        variant="modal"
        onChatSelect={(selectedChatId) => {
          console.log('Modal chat selected:', selectedChatId);
        }}
        onMessageSent={(message) => {
          console.log('Modal message sent:', message);
        }}
        onError={(error) => {
          console.error('Modal chat error:', error);
          onClose();
        }}
      />
    </div>
  );
};

// Custom Themed Chat - With custom styling
export const CustomThemedChat: React.FC = () => {
  const customTheme = {
    primary: '#6366f1', // Indigo
    secondary: '#64748b',
    accent: '#f59e0b',
    background: '#ffffff',
    surface: '#f8fafc',
    text: '#1e293b',
    textSecondary: '#64748b',
    border: '#e2e8f0',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
  };

  return (
    <div className="h-screen">
      <UniversalChat
        userRole="admin"
        userId="admin-custom"
        variant="full"
        theme="custom"
        customTheme={customTheme}
        features={{
          // Custom feature overrides
          hasVideoCall: true,
          canCreateGroups: true,
          maxFileSize: 100, // 100MB
        }}
        onChatSelect={(chatId) => {
          console.log('Custom themed chat selected:', chatId);
        }}
      />
    </div>
  );
};

// Integration Examples for Different Pages

// Example: Admin Messages Page
export const AdminMessagesPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">Admin Messages</h1>
        <div className="bg-white rounded-lg shadow-sm border h-[calc(100vh-200px)]">
          <AdminDashboardChat />
        </div>
      </div>
    </div>
  );
};

// Example: Provider Messages Page
export const ProviderMessagesPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-green-50">
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">Provider Messages</h1>
        <div className="bg-white rounded-lg shadow-sm border h-[calc(100vh-200px)]">
          <ProviderDashboardChat />
        </div>
      </div>
    </div>
  );
};

// Example: Customer Messages Page
export const CustomerMessagesPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-purple-50">
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">Customer Messages</h1>
        <div className="bg-white rounded-lg shadow-sm border h-[calc(100vh-200px)]">
          <CustomerDashboardChat />
        </div>
      </div>
    </div>
  );
};

// Example: Job Details Page with Chat
export const JobDetailsWithChat: React.FC<{ jobId: string; providerId: string }> = ({ 
  jobId, 
  providerId 
}) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Job Details */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h1 className="text-2xl font-bold mb-4">Job Details</h1>
              <p>Job ID: {jobId}</p>
              <p>Provider ID: {providerId}</p>
              {/* Job details content */}
            </div>
          </div>
          
          {/* Chat Panel */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border h-[600px]">
              <UniversalChat
                userRole="customer"
                userId="customer-job-123"
                recipientId={providerId}
                variant="full"
                theme="customer"
                className="h-full"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default {
  AdminDashboardChat,
  ProviderDashboardChat,
  CustomerDashboardChat,
  CompactChatWidget,
  ModalChat,
  CustomThemedChat,
  AdminMessagesPage,
  ProviderMessagesPage,
  CustomerMessagesPage,
  JobDetailsWithChat,
};
