import React from 'react';
import { Chat } from '@/types/chat';
import { ChatFeatures, ChatTheme } from '../config/roleConfigurations';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  Settings, 
  Phone, 
  Video, 
  MoreVertical, 
  X,
  ArrowLeft,
  Info
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

interface ChatHeaderProps {
  chat: Chat | null;
  userRole: 'admin' | 'provider' | 'customer';
  features: ChatFeatures;
  theme: ChatTheme;
  variant: 'compact' | 'full' | 'modal';
  onToggleParticipants?: () => void;
  onToggleSettings?: () => void;
  onClose?: () => void;
  onBack?: () => void;
}

/**
 * ChatHeader Component
 * 
 * Displays chat information, participant details, and role-specific actions.
 * Adapts its layout and available actions based on user role and variant.
 */
export const ChatHeader: React.FC<ChatHeaderProps> = ({
  chat,
  userRole,
  features,
  theme,
  variant,
  onToggleParticipants,
  onToggleSettings,
  onClose,
  onBack,
}) => {
  if (!chat) {
    return (
      <div className={cn(
        'chat-header',
        'flex items-center justify-between',
        'p-4 border-b border-[var(--chat-border)]',
        'bg-[var(--chat-surface)]'
      )}>
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-[var(--chat-border)] rounded-full animate-pulse" />
          <div className="space-y-1">
            <div className="w-24 h-4 bg-[var(--chat-border)] rounded animate-pulse" />
            <div className="w-16 h-3 bg-[var(--chat-border)] rounded animate-pulse" />
          </div>
        </div>
      </div>
    );
  }

  // Get the other participant (for direct chats)
  const otherParticipant = chat.type === 'direct' 
    ? chat.participants.find(p => p.role.name !== userRole)
    : null;

  // Get online participants count
  const onlineCount = chat.participants.filter(p => p.is_online).length;

  // Render participant info
  const renderParticipantInfo = () => {
    if (chat.type === 'group') {
      return (
        <div className="flex items-center space-x-2">
          <div className="flex -space-x-1">
            {chat.participants.slice(0, 3).map((participant, index) => (
              <Avatar key={participant.id} className="w-6 h-6 border-2 border-white">
                <AvatarImage src={participant.avatar} />
                <AvatarFallback className="text-xs">
                  {participant.name.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
            ))}
            {chat.participants.length > 3 && (
              <div className="w-6 h-6 bg-[var(--chat-secondary)] rounded-full flex items-center justify-center text-xs text-white border-2 border-white">
                +{chat.participants.length - 3}
              </div>
            )}
          </div>
          <div>
            <h3 className="font-medium text-sm">
              {chat.name || `Group (${chat.participants.length})`}
            </h3>
            <p className="text-xs text-[var(--chat-text-secondary)]">
              {onlineCount} online
            </p>
          </div>
        </div>
      );
    }

    if (otherParticipant) {
      return (
        <div className="flex items-center space-x-3">
          <div className="relative">
            <Avatar className="w-8 h-8">
              <AvatarImage src={otherParticipant.avatar} />
              <AvatarFallback>
                {otherParticipant.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            {features.canSeeOnlineStatus && otherParticipant.is_online && (
              <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
            )}
          </div>
          <div>
            <h3 className="font-medium text-sm">{otherParticipant.name}</h3>
            {features.canSeeOnlineStatus && (
              <p className="text-xs text-[var(--chat-text-secondary)]">
                {otherParticipant.is_online ? 'Online' : 
                 otherParticipant.last_seen ? `Last seen ${otherParticipant.last_seen}` : 'Offline'}
              </p>
            )}
          </div>
        </div>
      );
    }

    return (
      <div>
        <h3 className="font-medium text-sm">Chat</h3>
      </div>
    );
  };

  // Render action buttons
  const renderActions = () => {
    const actions = [];

    // Voice call button
    if (features.hasVideoCall && chat.type === 'direct') {
      actions.push(
        <Button
          key="voice"
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          title="Voice call"
        >
          <Phone className="h-4 w-4" />
        </Button>
      );
    }

    // Video call button
    if (features.hasVideoCall && chat.type === 'direct') {
      actions.push(
        <Button
          key="video"
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          title="Video call"
        >
          <Video className="h-4 w-4" />
        </Button>
      );
    }

    // Participants button (for full variant)
    if (variant === 'full' && features.canManageParticipants && onToggleParticipants) {
      actions.push(
        <Button
          key="participants"
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          onClick={onToggleParticipants}
          title="Show participants"
        >
          <Users className="h-4 w-4" />
        </Button>
      );
    }

    // Info button (for compact variant)
    if (variant === 'compact') {
      actions.push(
        <Button
          key="info"
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          title="Chat info"
        >
          <Info className="h-4 w-4" />
        </Button>
      );
    }

    return actions;
  };

  // Render more menu
  const renderMoreMenu = () => {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {features.canAccessHistory && (
            <DropdownMenuItem>
              View chat history
            </DropdownMenuItem>
          )}
          
          {features.canMuteConversations && (
            <DropdownMenuItem>
              Mute notifications
            </DropdownMenuItem>
          )}
          
          {features.canPinMessages && (
            <DropdownMenuItem>
              Pinned messages
            </DropdownMenuItem>
          )}
          
          <DropdownMenuSeparator />
          
          {onToggleSettings && (
            <DropdownMenuItem onClick={onToggleSettings}>
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </DropdownMenuItem>
          )}
          
          {features.canReportMessages && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600">
                Report chat
              </DropdownMenuItem>
            </>
          )}
          
          {features.canBlockUsers && chat.type === 'direct' && (
            <DropdownMenuItem className="text-red-600">
              Block user
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  return (
    <div className={cn(
      'chat-header',
      'flex items-center justify-between',
      'p-4 border-b border-[var(--chat-border)]',
      'bg-[var(--chat-surface)]',
      'min-h-[64px]'
    )}>
      {/* Left side - Back button (mobile) + Participant info */}
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        {(onBack || (variant === 'modal' && onClose)) && (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 flex-shrink-0"
            onClick={onBack || onClose}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
        )}
        
        <div className="flex-1 min-w-0">
          {renderParticipantInfo()}
        </div>
      </div>

      {/* Right side - Actions */}
      <div className="flex items-center space-x-1 flex-shrink-0">
        {renderActions()}
        {renderMoreMenu()}
        
        {variant === 'modal' && onClose && (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 ml-2"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
};

export default ChatHeader;
