
import React from 'react';
import { ProviderDashboardLayout } from '@/components/provider/ProviderDashboardLayout';
import { UniversalChat } from '@/components/chat';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { ChatProvider } from '@/contexts/ChatContext';

const ProviderMessages = () => {
  const { user } = useAuth();

  return (
    <ChatProvider>
      <ProviderDashboardLayout pageTitle="Messages">
        <div className="h-[calc(100vh-200px)] bg-white rounded-lg shadow-sm border">
          <UniversalChat
            userRole="provider"
            userId={user?.id || 'provider-user'}
            variant="full"
            theme="provider"
            onChatSelect={(chatId) => {
              // console.log('Provider selected chat:', chatId);
            }}
            onMessageSent={(message) => {
              // console.log('Provider sent message:', message);
            }}
            onError={(error) => {
              // console.error('Provider chat error:', error);
            }}
          />
        </div>
      </ProviderDashboardLayout>
    </ChatProvider>
  );
};

export default ProviderMessages;
